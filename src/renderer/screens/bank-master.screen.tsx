import React, { useState, useEffect } from 'react';
import { safeIpcInvoke } from '../utils/electron';
import { Modal } from '../components/modal';
import { Button } from '../components/button';

interface BankMaster {
  ftbnkid?: number;       // Auto-incrementing ID (primary key)
  ftbnkcode: string;      // Bank code (unique, max 5 chars)
  ftbnkname: string;      // Bank name (max 100 chars)
  ftstaactive: string;    // Status active (1=Active, 0=Inactive)
  fddateupd?: string;     // Date updated (TIMESTAMP)
  fttimeupd?: string;     // Time updated (VARCHAR 8)
  ftwhoupd?: string;      // Who updated (VARCHAR 30)
  fddateins?: string;     // Date inserted (TIMESTAMP)
  fttimeins?: string;     // Time inserted (VARCHAR 8)
  ftwhoins?: string;      // Who inserted (VARCHAR 30)
}

interface BankMasterResponse {
  success: boolean;
  message: string;
  data?: BankMaster | BankMaster[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  error?: string;
}

interface PaginationParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export function BankMasterScreen() {
  const [banks, setBanks] = useState<BankMaster[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingBank, setEditingBank] = useState<BankMaster | null>(null);
  const [formData, setFormData] = useState<Omit<BankMaster, 'ftbnkid' | 'fddateins' | 'fttimeins'>>({
    ftbnkcode: '',
    ftbnkname: '',
    ftstaactive: '1',
    ftwhoins: 'SYSTEM'
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [hasPreviousPage, setHasPreviousPage] = useState(false);

  // Search and sort state
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('ftbnkname');
  const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC'>('ASC');

  // Load banks on component mount
  useEffect(() => {
    initializeTable();
    loadBanks();
  }, []);

  // Reload when sort changes
  useEffect(() => {
    if (sortBy && sortOrder) {
      loadBanks(currentPage, searchTerm);
    }
  }, [sortBy, sortOrder]);

  const initializeTable = async () => {
    try {
      await safeIpcInvoke('init-bank-table');
    } catch (error) {
      console.error('Error initializing table:', error);
    }
  };

  const loadBanks = async (page: number = currentPage, search: string = searchTerm) => {
    setIsLoading(true);
    try {
      const params: PaginationParams = {
        page,
        pageSize,
        search: search.trim(),
        sortBy,
        sortOrder
      };

      const response: BankMasterResponse = await safeIpcInvoke('get-banks', params);
      if (response.success && Array.isArray(response.data)) {
        setBanks(response.data);

        // Update pagination state
        if (response.pagination) {
          setCurrentPage(response.pagination.currentPage);
          setTotalPages(response.pagination.totalPages);
          setTotalRecords(response.pagination.totalRecords);
          setHasNextPage(response.pagination.hasNextPage);
          setHasPreviousPage(response.pagination.hasPreviousPage);
        }
      } else {
        console.error('Failed to load banks:', response.message);
        setBanks([]);
      }
    } catch (error) {
      console.error('Error loading banks:', error);
      setBanks([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreate = () => {
    setEditingBank(null);
    setFormData({
      ftbnkcode: '',
      ftbnkname: '',
      ftstaactive: '1',
      ftwhoins: 'SYSTEM'
    });
    setIsModalOpen(true);
  };

  const handleEdit = (bank: BankMaster) => {
    setEditingBank(bank);
    setFormData({
      ftbnkcode: bank.ftbnkcode,
      ftbnkname: bank.ftbnkname,
      ftstaactive: bank.ftstaactive,
      ftwhoupd: 'SYSTEM'
    });
    setIsModalOpen(true);
  };

  const handleDelete = async (bank: BankMaster) => {
    if (!confirm(`Are you sure you want to delete ${bank.ftbnkname}?`)) {
      return;
    }

    if (!bank.ftbnkid) {
      alert('Cannot delete bank: Invalid bank ID');
      return;
    }

    try {
      const response: BankMasterResponse = await safeIpcInvoke('delete-bank', bank.ftbnkid);
      if (response.success) {
        await loadBanks(); // Reload the list
        alert('Bank deleted successfully');
      } else {
        alert(`Failed to delete bank: ${response.message}`);
      }
    } catch (error) {
      console.error('Error deleting bank:', error);
      alert('Error deleting bank');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.ftbnkcode.trim() || !formData.ftbnkname.trim()) {
      alert('Bank code and name are required');
      return;
    }

    if (formData.ftbnkcode.length > 5) {
      alert('Bank code must be 5 characters or less');
      return;
    }

    if (formData.ftbnkname.length > 100) {
      alert('Bank name must be 100 characters or less');
      return;
    }

    try {
      let response: BankMasterResponse;

      if (editingBank && editingBank.ftbnkid) {
        // Update existing bank
        response = await safeIpcInvoke('update-bank', editingBank.ftbnkid, formData);
      } else {
        // Create new bank
        response = await safeIpcInvoke('create-bank', formData);
      }

      if (response.success) {
        setIsModalOpen(false);
        await loadBanks(); // Reload the list
        alert(editingBank ? 'Bank updated successfully' : 'Bank created successfully');
      } else {
        alert(`Failed to ${editingBank ? 'update' : 'create'} bank: ${response.message}`);
      }
    } catch (error) {
      console.error('Error saving bank:', error);
      alert('Error saving bank');
    }
  };

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Pagination functions
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      loadBanks(newPage, searchTerm);
    }
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
    loadBanks(1, searchTerm);
  };

  const handleSearch = (search: string) => {
    setSearchTerm(search);
    setCurrentPage(1);
    loadBanks(1, search);
  };

  const handleSort = (column: string) => {
    const newSortOrder = sortBy === column && sortOrder === 'ASC' ? 'DESC' : 'ASC';
    setSortBy(column);
    setSortOrder(newSortOrder);
    loadBanks(currentPage, searchTerm);
  };

  const formatDate = (dateStr?: string) => {
    if (!dateStr) return '-';
    return new Date(dateStr).toLocaleDateString();
  };

  const formatTime = (timeStr?: string) => {
    if (!timeStr) return '-';
    return timeStr;
  };

  console.log("formData",formData)

  return (
    <div className="w-full max-w-7xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Bank Master Management</h1>
          <Button
            onClick={handleCreate}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
          >
            + Add New Bank
          </Button>
        </div>

        {/* Search and Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search by bank code or name..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="flex items-center gap-2">
            <label className="text-sm text-gray-600">Show:</label>
            <select
              value={pageSize}
              onChange={(e) => handlePageSizeChange(parseInt(e.target.value))}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
            <span className="text-sm text-gray-600">per page</span>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-gray-600">Loading banks...</span>
          </div>
        )}

        {/* Banks Table */}
        {!isLoading && (
          <div className="overflow-x-auto">
            <table className="min-w-full table-auto border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  <th
                    className="border border-gray-300 px-4 py-2 text-center cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('ftbnkid')}
                  >
                    <div className="flex items-center justify-center gap-1">
                      ID
                      {sortBy === 'ftbnkid' && (
                        <span className="text-xs">
                          {sortOrder === 'ASC' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th
                    className="border border-gray-300 px-4 py-2 text-left cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('ftbnkcode')}
                  >
                    <div className="flex items-center gap-1">
                      Bank Code
                      {sortBy === 'ftbnkcode' && (
                        <span className="text-xs">
                          {sortOrder === 'ASC' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th
                    className="border border-gray-300 px-4 py-2 text-left cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('ftbnkname')}
                  >
                    <div className="flex items-center gap-1">
                      Bank Name
                      {sortBy === 'ftbnkname' && (
                        <span className="text-xs">
                          {sortOrder === 'ASC' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th
                    className="border border-gray-300 px-4 py-2 text-center cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('ftstaactive')}
                  >
                    <div className="flex items-center justify-center gap-1">
                      Status
                      {sortBy === 'ftstaactive' && (
                        <span className="text-xs">
                          {sortOrder === 'ASC' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th
                    className="border border-gray-300 px-4 py-2 text-center cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('fddateins')}
                  >
                    <div className="flex items-center justify-center gap-1">
                      Created Date
                      {sortBy === 'fddateins' && (
                        <span className="text-xs">
                          {sortOrder === 'ASC' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th
                    className="border border-gray-300 px-4 py-2 text-center cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('fddateupd')}
                  >
                    <div className="flex items-center justify-center gap-1">
                      Updated Date
                      {sortBy === 'fddateupd' && (
                        <span className="text-xs">
                          {sortOrder === 'ASC' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                  <th className="border border-gray-300 px-4 py-2 text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                {banks.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="border border-gray-300 px-4 py-8 text-center text-gray-500">
                      No banks found. Click "Add New Bank" to create one.
                    </td>
                  </tr>
                ) : (
                  banks.map((bank) => (
                    <tr key={bank.ftbnkid || bank.ftbnkcode} className="hover:bg-gray-50">
                      <td className="border border-gray-300 px-4 py-2 text-center font-mono text-sm">{bank.ftbnkid}</td>
                      <td className="border border-gray-300 px-4 py-2 font-mono">{bank.ftbnkcode}</td>
                      <td className="border border-gray-300 px-4 py-2">{bank.ftbnkname}</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">
                        <span className={`px-2 py-1 rounded text-xs font-semibold ${
                          bank.ftstaactive === '1'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {bank.ftstaactive === '1' ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="border border-gray-300 px-4 py-2 text-center text-sm">
                        {formatDate(bank.fddateins)}
                        {bank.fttimeins && <br />}
                        <span className="text-gray-500">{formatTime(bank.fttimeins)}</span>
                      </td>
                      <td className="border border-gray-300 px-4 py-2 text-center text-sm">
                        {formatDate(bank.fddateupd)}
                        {bank.fttimeupd && <br />}
                        <span className="text-gray-500">{formatTime(bank.fttimeupd)}</span>
                      </td>
                      <td className="border border-gray-300 px-4 py-2 text-center">
                        <div className="flex gap-2 justify-center">
                          <Button
                            onClick={() => handleEdit(bank)}
                            className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 transition-colors"
                          >
                            Edit
                          </Button>
                          <Button
                            onClick={() => handleDelete(bank)}
                            className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600 transition-colors"
                          >
                            Delete
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination Controls */}
        {!isLoading && totalPages > 1 && (
          <div className="mt-6 flex flex-col sm:flex-row items-center justify-between gap-4">
            <div className="text-sm text-gray-600">
              Showing {banks.length > 0 ? ((currentPage - 1) * pageSize + 1) : 0} to {Math.min(currentPage * pageSize, totalRecords)} of {totalRecords} banks
            </div>

            <div className="flex items-center gap-2">
              <Button
                onClick={() => handlePageChange(1)}
                disabled={!hasPreviousPage}
                className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                First
              </Button>

              <Button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={!hasPreviousPage}
                className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                Previous
              </Button>

              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }

                  return (
                    <Button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`px-3 py-1 text-sm rounded ${
                        pageNum === currentPage
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      }`}
                    >
                      {pageNum}
                    </Button>
                  );
                })}
              </div>

              <Button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={!hasNextPage}
                className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                Next
              </Button>

              <Button
                onClick={() => handlePageChange(totalPages)}
                disabled={!hasNextPage}
                className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                Last
              </Button>
            </div>
          </div>
        )}

        {/* Summary */}
        {!isLoading && (
          <div className="mt-4 text-sm text-gray-600">
            Total Banks: {totalRecords} |
            Active: {banks.filter(b => b.ftstaactive === '1').length} |
            Inactive: {banks.filter(b => b.ftstaactive === '0').length}
            {searchTerm && ` | Filtered by: "${searchTerm}"`}
          </div>
        )}
      </div>

      {/* Create/Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      >
        <div className="mb-4">
          <h2 className="text-xl font-bold text-gray-800">
            {editingBank ? 'Edit Bank' : 'Create New Bank'}
          </h2>
        </div>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Bank Code <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.ftbnkcode}
              onChange={(e) => handleInputChange('ftbnkcode', e.target.value.toUpperCase())}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., BBL, SCB, KTB"
              maxLength={5}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Bank Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.ftbnkname}
              onChange={(e) => handleInputChange('ftbnkname', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Bangkok Bank, Siam Commercial Bank"
              maxLength={100}
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={formData.ftstaactive.toString()}
              onChange={(e) => handleInputChange('ftstaactive', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="1">Active</option>
              <option value="0">Inactive</option>
            </select>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              className="flex-1 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              {editingBank ? 'Update Bank' : 'Create Bank'}
            </Button>
            <Button
              type="button"
              onClick={() => setIsModalOpen(false)}
              className="flex-1 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            >
              Cancel
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
}

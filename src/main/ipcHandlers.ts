import axios from 'axios';
import { ipcMain } from 'electron';
import fs from 'fs';
import path from 'path';
import { watch } from 'chokidar';
import <PERSON> from 'papapar<PERSON>';
import { Client } from 'pg';
import {
  getSimpleCsvFolderPath,
  getSimpleCsvBackupFolderPath,
  ensureFolderExists,
  listCsvFiles,
  readFileContent,
  processAllCsvFiles,
  copyFileToBackup,
  deleteFile
} from './fileSystemUtils';

interface Todo {
  userId: number;
  id: number;
  title: string;
  completed: boolean;
}

interface CsvFileInfo {
  fileName: string;
  filePath: string;
  size: number;
  lastModified: Date;
  data?: any[];
  headers?: string[];
  error?: string;
}

interface PostgreSQLConnectionConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl?: boolean;
}

interface PostgreSQLConnectionResult {
  success: boolean;
  message: string;
  connectionTime?: number;
  serverVersion?: string;
  error?: string;
}

interface PCloudConnectionConfig {
  username: string;
  password: string;
  region?: 'us' | 'eu'; // US or Europe data center
}

interface PCloudConnectionResult {
  success: boolean;
  message: string;
  connectionTime?: number;
  userInfo?: {
    email: string;
    emailVerified: boolean;
    premium: boolean;
    quota: number;
    usedQuota: number;
    language: string;
    registered: string;
  };
  error?: string;
}

interface BankMaster {
  ftbnkid?: number;       // Auto-incrementing ID (primary key)
  ftbnkcode: string;      // Bank code (unique, max 5 chars)
  ftbnkname: string;      // Bank name (max 100 chars)
  ftstaactive: string;    // Status active (1=Active, 0=Inactive)
  fddateupd?: string;     // Date updated (TIMESTAMP)
  fttimeupd?: string;     // Time updated (VARCHAR 8)
  ftwhoupd?: string;      // Who updated (VARCHAR 30)
  fddateins?: string;     // Date inserted (TIMESTAMP)
  fttimeins?: string;     // Time inserted (VARCHAR 8)
  ftwhoins?: string;      // Who inserted (VARCHAR 30)
}

interface BankMasterResponse {
  success: boolean;
  message: string;
  data?: BankMaster | BankMaster[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  error?: string;
}

interface PaginationParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

interface FolderWatcherStatus {
  isWatching: boolean;
  folderPath: string;
  backupFolderPath: string;
  filesDetected: CsvFileInfo[];
  lastUpdate: Date;
  autoProcessing: boolean;
  processedCount: number;
  failedCount: number;
  isPaused: boolean; // True when no files in folder and processing is paused
}

interface WorkflowConfig {
  autoProcessing: boolean;
  processDelay: number; // milliseconds to wait before processing new files
  enableBackup: boolean;
  enableCleanup: boolean;
}

// Global variables for folder watching
let folderWatcher: any = null;
let periodicChecker: NodeJS.Timeout | null = null;
let lastEmptyFolderLog: number = 0; // Track when we last logged empty folder message
let workflowConfig: WorkflowConfig = {
  autoProcessing: true,  // Enable auto-processing by default
  processDelay: 2000, // 2 seconds delay
  enableBackup: true,
  enableCleanup: true
};

let watcherStatus: FolderWatcherStatus = {
  isWatching: false,
  folderPath: '',
  backupFolderPath: '',
  filesDetected: [],
  lastUpdate: new Date(),
  autoProcessing: false,
  processedCount: 0,
  failedCount: 0,
  isPaused: false
};



// Periodic checker function to scan for new CSV files
async function checkForNewFiles(): Promise<void> {
  if (!workflowConfig.autoProcessing || !watcherStatus.isWatching) {
    return;
  }

  try {
    const csvFolderPath = getSimpleCsvFolderPath();
    const currentFiles = fs.readdirSync(csvFolderPath)
      .filter(file => file.toLowerCase().endsWith('.csv'))
      .map(file => path.join(csvFolderPath, file));

    // If no CSV files found, log status and skip processing
    if (currentFiles.length === 0) {
      // Only log this message occasionally to avoid spam
      const now = Date.now();
      if (!lastEmptyFolderLog || now - lastEmptyFolderLog > 30000) { // Log every 30 seconds
        console.log(`📂 Folder check: No CSV files found in ${csvFolderPath} - auto-processing paused`);
        lastEmptyFolderLog = now;
      }

      // Clear detected files list if folder is empty
      if (watcherStatus.filesDetected.length > 0) {
        console.log(`🧹 Clearing detected files list - folder is empty`);
        watcherStatus.filesDetected = [];
        watcherStatus.lastUpdate = new Date();
      }

      // Set paused status
      if (!watcherStatus.isPaused) {
        watcherStatus.isPaused = true;
        watcherStatus.lastUpdate = new Date();
      }

      return; // Stop processing when no files
    }

    // Reset the empty log timer when files are found
    if (lastEmptyFolderLog) {
      console.log(`📁 CSV files detected - resuming auto-processing`);
      lastEmptyFolderLog = 0;
    }

    // Resume processing if it was paused
    if (watcherStatus.isPaused) {
      console.log(`▶️ Resuming auto-processing - files detected`);
      watcherStatus.isPaused = false;
      watcherStatus.lastUpdate = new Date();
    }

    for (const filePath of currentFiles) {
      // Check if this file is already in our detected files list
      const fileName = path.basename(filePath);
      const alreadyDetected = watcherStatus.filesDetected.some(f => f.fileName === fileName);

      if (!alreadyDetected) {
        console.log(`🔍 Periodic check found new CSV file: ${fileName}`);

        // Add to detected files list
        const fileInfo = await processCsvFile(filePath);
        watcherStatus.filesDetected.push(fileInfo);
        watcherStatus.lastUpdate = new Date();

        console.log(`📊 Added to detected files: ${fileInfo.fileName}, rows: ${fileInfo.data?.length || 0}`);

        // Auto-process the file
        console.log(`⚡ Auto-processing new file: ${fileInfo.fileName}`);
        setTimeout(async () => {
          try {
            await processFileWorkflow(filePath);
          } catch (error) {
            console.error(`❌ Auto-processing failed for ${fileInfo.fileName}:`, error);
            watcherStatus.failedCount++;
          }
        }, workflowConfig.processDelay);
      }
    }
  } catch (error) {
    console.error('Error in periodic file check:', error);
  }
}

// Start periodic checking
function startPeriodicChecker(): void {
  if (periodicChecker) {
    clearInterval(periodicChecker);
  }

  // Check every 5 seconds for new files
  periodicChecker = setInterval(checkForNewFiles, 5000);
  console.log('📅 Started periodic file checker (every 5 seconds)');
}

// Stop periodic checking
function stopPeriodicChecker(): void {
  if (periodicChecker) {
    clearInterval(periodicChecker);
    periodicChecker = null;
    console.log('📅 Stopped periodic file checker');
  }
}

// Workflow function to process a single file (read, backup, delete)
async function processFileWorkflow(filePath: string): Promise<void> {
  const fileName = path.basename(filePath);
  console.log(`\n🔄 Starting workflow for: ${fileName}`);

  try {
    // Step 1: Read and log file data
    console.log(`📖 Step 1: Reading file data...`);
    const content = readFileContent(filePath);
    if (!content) {
      throw new Error('Failed to read file content');
    }

    // Log file information
    const stats = fs.statSync(filePath);
    console.log(`📄 File: ${fileName}`);
    console.log(`📊 Size: ${stats.size} bytes`);
    console.log(`📅 Modified: ${stats.mtime.toISOString()}`);
    console.log(`📝 Content preview (first 300 chars):`);
    console.log(content.substring(0, 300) + (content.length > 300 ? '...' : ''));
    console.log('---');

    // Parse CSV data and log it
    const csvData = await new Promise<any[]>((resolve, reject) => {
      Papa.parse(content, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          if (results.errors.length > 0) {
            reject(new Error(`CSV parsing error: ${results.errors[0].message}`));
            return;
          }
          resolve(results.data as any[]);
        },
        error: (error: any) => {
          reject(new Error(`Error parsing CSV: ${error.message}`));
        }
      });
    });

    console.log(`📊 CSV Data (${csvData.length} rows):`);
    console.log(JSON.stringify(csvData, null, 2));

    // Step 2: Create backup if enabled
    if (workflowConfig.enableBackup) {
      console.log(`💾 Step 2: Creating backup...`);
      const backupPath = copyFileToBackup(filePath, watcherStatus.backupFolderPath);
      if (!backupPath) {
        throw new Error('Failed to create backup');
      }
      console.log(`✅ Backup created: ${backupPath}`);
    } else {
      console.log(`⏭️  Step 2: Backup disabled, skipping...`);
    }

    // Step 3: Delete original file if cleanup is enabled
    if (workflowConfig.enableCleanup) {
      console.log(`🗑️  Step 3: Deleting original file...`);
      const deleted = deleteFile(filePath);
      if (!deleted) {
        throw new Error('Failed to delete original file');
      }
      console.log(`✅ Original file deleted: ${fileName}`);

      // Remove from detected files list
      watcherStatus.filesDetected = watcherStatus.filesDetected.filter(f => f.filePath !== filePath);
    } else {
      console.log(`⏭️  Step 3: Cleanup disabled, keeping original file...`);
    }

    watcherStatus.processedCount++;
    watcherStatus.lastUpdate = new Date();
    console.log(`🎉 Workflow completed successfully for: ${fileName}`);

  } catch (error) {
    console.error(`❌ Workflow failed for ${fileName}:`, error);
    watcherStatus.failedCount++;
    throw error;
  }
}

// Utility function to process CSV file
async function processCsvFile(filePath: string): Promise<CsvFileInfo> {
  const fileName = path.basename(filePath);
  const stats = fs.statSync(filePath);

  const fileInfo: CsvFileInfo = {
    fileName,
    filePath,
    size: stats.size,
    lastModified: stats.mtime
  };

  try {
    const fileContent = fs.readFileSync(filePath, 'utf8');

    return new Promise((resolve) => {
      Papa.parse(fileContent, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          if (results.errors.length > 0) {
            fileInfo.error = `CSV parsing error: ${results.errors[0].message}`;
          } else {
            const data = results.data as any[];
            if (data.length > 0) {
              fileInfo.headers = Object.keys(data[0]);
              fileInfo.data = data;
            } else {
              fileInfo.error = 'No data found in CSV file';
            }
          }
          resolve(fileInfo);
        },
        error: (error: any) => {
          fileInfo.error = `Error reading file: ${error.message}`;
          resolve(fileInfo);
        }
      });
    });
  } catch (error) {
    fileInfo.error = `File read error: ${error instanceof Error ? error.message : 'Unknown error'}`;
    return fileInfo;
  }
}

export function setupIpcHandlers() {
  console.log('Setting up IPC handlers...');

  ipcMain.handle('fetch-data', async () => {
    try {
      console.log('fetch-data handler called');
      const response = await axios.get<Todo[]>('https://jsonplaceholder.typicode.com/todos');
      console.log('API response received, data length:', response.data.length);
      return response.data;
    } catch (error) {
      console.error('Error fetching todos:', error);
      throw error;
    }
  });

  // Start folder watching
  ipcMain.handle('start-folder-watching', async () => {
    try {
      console.log('start-folder-watching handler called');

      if (folderWatcher) {
        console.log('Folder watcher already running');
        return watcherStatus;
      }

      const csvFolderPath = getSimpleCsvFolderPath();
      const backupFolderPath = getSimpleCsvBackupFolderPath();
      ensureFolderExists(csvFolderPath);
      ensureFolderExists(backupFolderPath);

      watcherStatus = {
        isWatching: true,
        folderPath: csvFolderPath,
        backupFolderPath: backupFolderPath,
        filesDetected: [],
        lastUpdate: new Date(),
        autoProcessing: workflowConfig.autoProcessing,
        processedCount: 0,
        failedCount: 0,
        isPaused: false
      };

      // Scan existing files
      const existingFiles = fs.readdirSync(csvFolderPath)
        .filter(file => file.toLowerCase().endsWith('.csv'))
        .map(file => path.join(csvFolderPath, file));

      console.log(`📁 Found ${existingFiles.length} existing CSV files in folder`);

      for (const filePath of existingFiles) {
        const fileInfo = await processCsvFile(filePath);
        watcherStatus.filesDetected.push(fileInfo);

        // Auto-process existing files if auto-processing is enabled
        if (workflowConfig.autoProcessing) {
          console.log(`⚡ Auto-processing existing file: ${fileInfo.fileName}`);

          // Add a small delay to avoid overwhelming the system
          setTimeout(async () => {
            try {
              await processFileWorkflow(filePath);
            } catch (error) {
              console.error(`❌ Auto-processing failed for existing file ${fileInfo.fileName}:`, error);
              watcherStatus.failedCount++;
            }
          }, 1000 + (watcherStatus.filesDetected.length * 500)); // Stagger processing
        }
      }

      // Start watching for new files
      folderWatcher = watch(csvFolderPath, {
        ignored: /[\/\\]\./,
        persistent: true,
        ignoreInitial: true
      });

      folderWatcher.on('add', async (filePath: string) => {
        if (filePath.toLowerCase().endsWith('.csv')) {
          console.log(`🔍 New CSV file detected: ${filePath}`);
          const fileInfo = await processCsvFile(filePath);
          watcherStatus.filesDetected.push(fileInfo);
          watcherStatus.lastUpdate = new Date();

          console.log(`📊 Processed CSV file: ${fileInfo.fileName}, rows: ${fileInfo.data?.length || 0}`);

          // Auto-processing workflow
          if (workflowConfig.autoProcessing) {
            console.log(`⚡ Auto-processing enabled, starting workflow for: ${fileInfo.fileName}`);

            // Add delay before processing
            setTimeout(async () => {
              try {
                await processFileWorkflow(filePath);
              } catch (error) {
                console.error(`❌ Auto-processing failed for ${fileInfo.fileName}:`, error);
                watcherStatus.failedCount++;
              }
            }, workflowConfig.processDelay);
          }
        }
      });

      folderWatcher.on('change', async (filePath: string) => {
        if (filePath.toLowerCase().endsWith('.csv')) {
          console.log(`CSV file changed: ${filePath}`);
          // Update existing file info
          const existingIndex = watcherStatus.filesDetected.findIndex(f => f.filePath === filePath);
          if (existingIndex !== -1) {
            const fileInfo = await processCsvFile(filePath);
            watcherStatus.filesDetected[existingIndex] = fileInfo;
            watcherStatus.lastUpdate = new Date();
          }
        }
      });

      folderWatcher.on('unlink', (filePath: string) => {
        if (filePath.toLowerCase().endsWith('.csv')) {
          console.log(`CSV file removed: ${filePath}`);
          watcherStatus.filesDetected = watcherStatus.filesDetected.filter(f => f.filePath !== filePath);
          watcherStatus.lastUpdate = new Date();
        }
      });

      folderWatcher.on('error', (error: Error) => {
        console.error('Folder watcher error:', error);
      });

      // Start periodic checker as backup
      startPeriodicChecker();

      console.log(`Started watching folder: ${csvFolderPath}`);
      return watcherStatus;
    } catch (error) {
      console.error('Error starting folder watcher:', error);
      throw error;
    }
  });

  // Stop folder watching
  ipcMain.handle('stop-folder-watching', async () => {
    try {
      console.log('stop-folder-watching handler called');

      if (folderWatcher) {
        await folderWatcher.close();
        folderWatcher = null;
      }

      // Stop periodic checker
      stopPeriodicChecker();

      watcherStatus.isWatching = false;
      watcherStatus.lastUpdate = new Date();

      console.log('Stopped folder watching');
      return watcherStatus;
    } catch (error) {
      console.error('Error stopping folder watcher:', error);
      throw error;
    }
  });

  // Get folder watcher status
  ipcMain.handle('get-folder-watcher-status', async () => {
    return watcherStatus;
  });

  // Get CSV file data
  ipcMain.handle('get-csv-file-data', async (_event, fileName: string) => {
    try {
      const fileInfo = watcherStatus.filesDetected.find(f => f.fileName === fileName);
      if (!fileInfo) {
        throw new Error(`File not found: ${fileName}`);
      }
      return fileInfo;
    } catch (error) {
      console.error('Error getting CSV file data:', error);
      throw error;
    }
  });

  // Initialize CSV folder
  ipcMain.handle('initialize-csv-folder', async () => {
    try {
      const csvFolderPath = getSimpleCsvFolderPath();
      const success = ensureFolderExists(csvFolderPath);

      if (success) {
        // List existing CSV files
        const existingFiles = listCsvFiles(csvFolderPath);
        console.log(`CSV folder initialized at: ${csvFolderPath}`);
        console.log(`Found ${existingFiles.length} existing CSV files`);

        return {
          success: true,
          folderPath: csvFolderPath,
          existingFiles: existingFiles.length
        };
      } else {
        throw new Error('Failed to create CSV folder');
      }
    } catch (error) {
      console.error('Error initializing CSV folder:', error);
      throw error;
    }
  });

  // Open CSV folder in file explorer
  ipcMain.handle('open-csv-folder', async () => {
    try {
      const csvFolderPath = getSimpleCsvFolderPath();
      ensureFolderExists(csvFolderPath);

      // Use shell to open folder
      const { shell } = require('electron');
      await shell.openPath(csvFolderPath);

      return { success: true, folderPath: csvFolderPath };
    } catch (error) {
      console.error('Error opening CSV folder:', error);
      throw error;
    }
  });

  // Process CSV file from path (for integration with existing upload functionality)
  ipcMain.handle('process-csv-from-path', async (_event, filePath: string) => {
    try {
      const fileContent = readFileContent(filePath);
      if (!fileContent) {
        throw new Error('Failed to read file content');
      }

      return new Promise((resolve, reject) => {
        Papa.parse(fileContent, {
          header: true,
          skipEmptyLines: true,
          complete: (results) => {
            if (results.errors.length > 0) {
              reject(new Error(`CSV parsing error: ${results.errors[0].message}`));
              return;
            }

            const data = results.data as any[];
            if (data.length > 0) {
              resolve({
                data,
                headers: Object.keys(data[0]),
                rowCount: data.length,
                fileName: path.basename(filePath)
              });
            } else {
              reject(new Error('No data found in CSV file'));
            }
          },
          error: (error: any) => {
            reject(new Error(`Error reading file: ${error.message}`));
          }
        });
      });
    } catch (error) {
      console.error('Error processing CSV from path:', error);
      throw error;
    }
  });

  // Enable/disable auto-processing workflow
  ipcMain.handle('set-auto-processing', async (_event, enabled: boolean) => {
    try {
      workflowConfig.autoProcessing = enabled;
      watcherStatus.autoProcessing = enabled;
      watcherStatus.lastUpdate = new Date();

      console.log(`Auto-processing ${enabled ? 'enabled' : 'disabled'}`);
      return { success: true, autoProcessing: enabled };
    } catch (error) {
      console.error('Error setting auto-processing:', error);
      throw error;
    }
  });

  // Get workflow configuration
  ipcMain.handle('get-workflow-config', async () => {
    return workflowConfig;
  });

  // Update workflow configuration
  ipcMain.handle('update-workflow-config', async (_event, config: Partial<WorkflowConfig>) => {
    try {
      workflowConfig = { ...workflowConfig, ...config };
      watcherStatus.autoProcessing = workflowConfig.autoProcessing;
      watcherStatus.lastUpdate = new Date();

      console.log('Workflow config updated:', workflowConfig);
      return { success: true, config: workflowConfig };
    } catch (error) {
      console.error('Error updating workflow config:', error);
      throw error;
    }
  });

  // Manually process all files in folder
  ipcMain.handle('process-all-csv-files', async () => {
    try {
      console.log('🚀 Manual processing of all CSV files requested');
      const csvFolderPath = getSimpleCsvFolderPath();
      const backupFolderPath = getSimpleCsvBackupFolderPath();

      const result = processAllCsvFiles(csvFolderPath, backupFolderPath);

      watcherStatus.processedCount += result.processed.length;
      watcherStatus.failedCount += result.failed.length;
      watcherStatus.filesDetected = watcherStatus.filesDetected.filter(
        f => !result.processed.includes(f.fileName)
      );
      watcherStatus.lastUpdate = new Date();

      return {
        success: true,
        ...result,
        totalProcessed: watcherStatus.processedCount,
        totalFailed: watcherStatus.failedCount
      };
    } catch (error) {
      console.error('Error processing all CSV files:', error);
      throw error;
    }
  });

  // Reset processing counters
  ipcMain.handle('reset-processing-counters', async () => {
    try {
      watcherStatus.processedCount = 0;
      watcherStatus.failedCount = 0;
      watcherStatus.lastUpdate = new Date();

      console.log('Processing counters reset');
      return { success: true };
    } catch (error) {
      console.error('Error resetting counters:', error);
      throw error;
    }
  });

  // Auto-start folder watching (called when renderer is ready)
  ipcMain.handle('auto-start-watching', async () => {
    try {
      console.log('🚀 Auto-starting folder watching...');

      if (folderWatcher) {
        console.log('Folder watcher already running');
        return watcherStatus;
      }

      const csvFolderPath = getSimpleCsvFolderPath();
      const backupFolderPath = getSimpleCsvBackupFolderPath();
      ensureFolderExists(csvFolderPath);
      ensureFolderExists(backupFolderPath);

      watcherStatus = {
        isWatching: true,
        folderPath: csvFolderPath,
        backupFolderPath: backupFolderPath,
        filesDetected: [],
        lastUpdate: new Date(),
        autoProcessing: workflowConfig.autoProcessing,
        processedCount: 0,
        failedCount: 0,
        isPaused: false
      };

      // Scan and auto-process existing files
      const existingFiles = fs.readdirSync(csvFolderPath)
        .filter(file => file.toLowerCase().endsWith('.csv'))
        .map(file => path.join(csvFolderPath, file));

      console.log(`📁 Auto-start: Found ${existingFiles.length} existing CSV files`);

      for (const filePath of existingFiles) {
        const fileInfo = await processCsvFile(filePath);
        watcherStatus.filesDetected.push(fileInfo);

        // Auto-process existing files immediately
        if (workflowConfig.autoProcessing) {
          console.log(`⚡ Auto-processing existing file: ${fileInfo.fileName}`);

          // Process with staggered delay
          setTimeout(async () => {
            try {
              await processFileWorkflow(filePath);
            } catch (error) {
              console.error(`❌ Auto-processing failed for ${fileInfo.fileName}:`, error);
              watcherStatus.failedCount++;
            }
          }, 1000 + (watcherStatus.filesDetected.length * 500));
        }
      }

      // Start watching for new files
      folderWatcher = watch(csvFolderPath, {
        ignored: /[\/\\]\./,
        persistent: true,
        ignoreInitial: true
      });

      folderWatcher.on('add', async (filePath: string) => {
        if (filePath.toLowerCase().endsWith('.csv')) {
          console.log(`🔍 New CSV file detected: ${filePath}`);
          const fileInfo = await processCsvFile(filePath);
          watcherStatus.filesDetected.push(fileInfo);
          watcherStatus.lastUpdate = new Date();

          console.log(`📊 Processed CSV file: ${fileInfo.fileName}, rows: ${fileInfo.data?.length || 0}`);

          // Auto-processing workflow
          if (workflowConfig.autoProcessing) {
            console.log(`⚡ Auto-processing enabled, starting workflow for: ${fileInfo.fileName}`);

            setTimeout(async () => {
              try {
                await processFileWorkflow(filePath);
              } catch (error) {
                console.error(`❌ Auto-processing failed for ${fileInfo.fileName}:`, error);
                watcherStatus.failedCount++;
              }
            }, workflowConfig.processDelay);
          }
        }
      });

      folderWatcher.on('change', async (filePath: string) => {
        if (filePath.toLowerCase().endsWith('.csv')) {
          console.log(`📝 CSV file changed: ${filePath}`);
          const existingIndex = watcherStatus.filesDetected.findIndex(f => f.filePath === filePath);
          if (existingIndex !== -1) {
            const fileInfo = await processCsvFile(filePath);
            watcherStatus.filesDetected[existingIndex] = fileInfo;
            watcherStatus.lastUpdate = new Date();
          }
        }
      });

      folderWatcher.on('unlink', (filePath: string) => {
        if (filePath.toLowerCase().endsWith('.csv')) {
          console.log(`🗑️ CSV file removed: ${filePath}`);
          watcherStatus.filesDetected = watcherStatus.filesDetected.filter(f => f.filePath !== filePath);
          watcherStatus.lastUpdate = new Date();
        }
      });

      folderWatcher.on('error', (error: Error) => {
        console.error('Folder watcher error:', error);
      });

      // Start periodic checker as backup
      startPeriodicChecker();

      console.log(`✅ Auto-started watching folder: ${csvFolderPath}`);
      console.log(`🔄 Auto-processing: ${workflowConfig.autoProcessing ? 'ENABLED' : 'DISABLED'}`);
      console.log(`📅 Periodic checker: ENABLED (every 5 seconds)`);

      return watcherStatus;
    } catch (error) {
      console.error('Error auto-starting folder watcher:', error);
      throw error;
    }
  });

  // PostgreSQL connection test handler
  ipcMain.handle('test-postgresql-connection', async (_event, config: PostgreSQLConnectionConfig): Promise<PostgreSQLConnectionResult> => {
    const startTime = Date.now();
    let client: Client | null = null;

    try {
      console.log('🔍 Testing PostgreSQL connection...');
      console.log(`📍 Host: ${config.host}:${config.port}`);
      console.log(`🗄️ Database: ${config.database}`);
      console.log(`👤 User: ${config.user}`);

      // Create PostgreSQL client
      client = new Client({
        host: config.host,
        port: config.port,
        database: config.database,
        user: config.user,
        password: config.password,
        ssl: config.ssl ? { rejectUnauthorized: false } : false,
        connectionTimeoutMillis: 10000, // 10 seconds timeout
        query_timeout: 5000, // 5 seconds query timeout
      });

      // Connect to the database
      await client.connect();
      console.log('✅ PostgreSQL connection established');

      // Test with a simple query to get server version
      const result = await client.query('SELECT version() as server_version');
      const serverVersion = result.rows[0]?.server_version || 'Unknown';

      const connectionTime = Date.now() - startTime;
      console.log(`⏱️ Connection time: ${connectionTime}ms`);
      console.log(`🔧 Server version: ${serverVersion}`);

      return {
        success: true,
        message: 'PostgreSQL connection successful',
        connectionTime,
        serverVersion: serverVersion.split(' ')[0] + ' ' + serverVersion.split(' ')[1], // Extract version number
      };

    } catch (error: any) {
      const connectionTime = Date.now() - startTime;
      console.error('❌ PostgreSQL connection failed:', error.message);

      let errorMessage = 'Connection failed';

      if (error.code === 'ECONNREFUSED') {
        errorMessage = 'Connection refused - server may be down or unreachable';
      } else if (error.code === 'ENOTFOUND') {
        errorMessage = 'Host not found - check the hostname/IP address';
      } else if (error.code === 'ECONNRESET') {
        errorMessage = 'Connection reset - check network connectivity';
      } else if (error.code === '28P01') {
        errorMessage = 'Authentication failed - check username/password';
      } else if (error.code === '3D000') {
        errorMessage = 'Database does not exist';
      } else if (error.code === '28000') {
        errorMessage = 'Invalid authorization specification';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Connection timeout - server may be slow or unreachable';
      } else {
        errorMessage = error.message;
      }

      return {
        success: false,
        message: errorMessage,
        connectionTime,
        error: error.code || 'UNKNOWN_ERROR',
      };

    } finally {
      // Always close the connection
      if (client) {
        try {
          await client.end();
          console.log('🔌 PostgreSQL connection closed');
        } catch (closeError) {
          console.error('Error closing PostgreSQL connection:', closeError);
        }
      }
    }
  });

  // pCloud connection test handler
  ipcMain.handle('test-pcloud-connection', async (_event, config: PCloudConnectionConfig): Promise<PCloudConnectionResult> => {
    const startTime = Date.now();

    try {
      console.log('🔍 Testing pCloud connection...');
      console.log(`👤 User: ${config.username}`);
      console.log(`🌍 Region: ${config.region || 'auto-detect'}`);

      // Determine API endpoint based on region
      const apiHost = config.region === 'eu' ? 'eapi.pcloud.com' : 'api.pcloud.com';
      console.log(`📍 API Host: ${apiHost}`);

      // Prepare authentication parameters
      const authParams = new URLSearchParams({
        username: config.username,
        password: config.password,
        getauth: '1', // Request auth token
        logout: '1'   // Logout any existing session
      });

      // Make API call to userinfo endpoint for authentication test
      const apiUrl = `https://${apiHost}/userinfo?${authParams.toString()}`;

      console.log('🔗 Making API request...');
      const response = await axios.get(apiUrl, {
        timeout: 10000, // 10 seconds timeout
        headers: {
          'User-Agent': 'Electron-pCloud-Test/1.0'
        }
      });

      const connectionTime = Date.now() - startTime;
      console.log(`⏱️ Connection time: ${connectionTime}ms`);

      if (response.data.result === 0) {
        // Success
        console.log('✅ pCloud connection successful');
        console.log(`📧 Email: ${response.data.email}`);
        console.log(`💎 Premium: ${response.data.premium ? 'Yes' : 'No'}`);
        console.log(`💾 Quota: ${Math.round(response.data.quota / (1024 * 1024 * 1024))}GB`);
        console.log(`📊 Used: ${Math.round(response.data.usedquota / (1024 * 1024 * 1024))}GB`);

        return {
          success: true,
          message: 'pCloud connection successful',
          connectionTime,
          userInfo: {
            email: response.data.email,
            emailVerified: response.data.emailverified,
            premium: response.data.premium,
            quota: response.data.quota,
            usedQuota: response.data.usedquota,
            language: response.data.language,
            registered: response.data.registered
          }
        };

      } else {
        // API returned an error
        console.error('❌ pCloud API error:', response.data);

        let errorMessage = 'Authentication failed';

        switch (response.data.result) {
          case 1000:
            errorMessage = 'Login required - invalid credentials';
            break;
          case 2000:
            errorMessage = 'Login failed - check username and password';
            break;
          case 4000:
            errorMessage = 'Too many login attempts from this IP address';
            break;
          default:
            errorMessage = `API error: ${response.data.error || 'Unknown error'}`;
        }

        return {
          success: false,
          message: errorMessage,
          connectionTime,
          error: `API_ERROR_${response.data.result}`
        };
      }

    } catch (error: any) {
      const connectionTime = Date.now() - startTime;
      console.error('❌ pCloud connection failed:', error.message);

      let errorMessage = 'Connection failed';

      if (error.code === 'ECONNREFUSED') {
        errorMessage = 'Connection refused - pCloud servers may be unreachable';
      } else if (error.code === 'ENOTFOUND') {
        errorMessage = 'DNS resolution failed - check internet connection';
      } else if (error.code === 'ECONNRESET') {
        errorMessage = 'Connection reset - network connectivity issue';
      } else if (error.code === 'ETIMEDOUT' || error.message.includes('timeout')) {
        errorMessage = 'Connection timeout - pCloud servers may be slow';
      } else if (error.response?.status === 401) {
        errorMessage = 'Authentication failed - invalid username or password';
      } else if (error.response?.status === 403) {
        errorMessage = 'Access forbidden - account may be suspended';
      } else if (error.response?.status >= 500) {
        errorMessage = 'pCloud server error - try again later';
      } else {
        errorMessage = error.message;
      }

      return {
        success: false,
        message: errorMessage,
        connectionTime,
        error: error.code || 'UNKNOWN_ERROR'
      };
    }
  });

  // Bank Master CRUD handlers
  // Get database connection helper
  const getDbConnection = async (config?: PostgreSQLConnectionConfig): Promise<Client> => {
    const dbConfig = config || {
      host: 'ep-dawn-fog-a1jk7z7f-pooler.ap-southeast-1.aws.neon.tech',
      port: 5432,
      database: 'neondb',
      user: 'neondb_owner',
      password: 'npg_v1aKnJdNXif4',
      ssl: true,
    };

    const client = new Client({
      host: dbConfig.host,
      port: dbConfig.port,
      database: dbConfig.database,
      user: dbConfig.user,
      password: dbConfig.password,
      ssl: dbConfig.ssl ? { rejectUnauthorized: false } : false,
      connectionTimeoutMillis: 10000,
      query_timeout: 30000,
    });

    await client.connect();
    return client;
  };

  // Create tmst_bank table if not exists (using actual schema)
  ipcMain.handle('init-bank-table', async (): Promise<BankMasterResponse> => {
    let client: Client | null = null;

    try {
      console.log('🔧 Checking bank master table...');
      client = await getDbConnection();

      // Check if table exists and create if needed
      const checkTableQuery = `
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name = 'tmst_bank'
        );
      `;

      const tableExists = await client.query(checkTableQuery);

      if (!tableExists.rows[0].exists) {
        const createTableQuery = `
          CREATE TABLE tmst_bank (
            ftbnkid INT GENERATED BY DEFAULT AS IDENTITY,
            ftbnkcode VARCHAR(5) NOT NULL,
            ftbnkname VARCHAR(100),
            ftstaactive VARCHAR(1),
            fddateupd TIMESTAMP,
            fttimeupd VARCHAR(8),
            ftwhoupd VARCHAR(30),
            fddateins TIMESTAMP,
            fttimeins VARCHAR(8),
            ftwhoins VARCHAR(30),
            CONSTRAINT pk_tmst_bank PRIMARY KEY (ftbnkid),
            CONSTRAINT uq_tmst_bank_ftbnkcode UNIQUE (ftbnkcode)
          );

          -- Create index for faster lookups
          CREATE INDEX IF NOT EXISTS idx_tmst_bank_active ON tmst_bank(ftstaactive);
        `;

        await client.query(createTableQuery);
        console.log('✅ Bank master table created successfully');
      } else {
        console.log('✅ Bank master table already exists');
      }

      return {
        success: true,
        message: 'Bank master table ready'
      };

    } catch (error: any) {
      console.error('❌ Error checking/creating bank table:', error.message);
      return {
        success: false,
        message: 'Failed to initialize bank table',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Get banks with pagination
  ipcMain.handle('get-banks', async (_event, params: PaginationParams = {}): Promise<BankMasterResponse> => {
    let client: Client | null = null;

    try {
      const {
        page = 1,
        pageSize = 10,
        search = '',
        sortBy = 'ftbnkname',
        sortOrder = 'ASC'
      } = params;

      console.log(`📋 Fetching banks - Page: ${page}, Size: ${pageSize}, Search: "${search}"`);
      client = await getDbConnection();

      // Build WHERE clause for search
      let whereClause = '';
      let searchParams: any[] = [];
      let paramIndex = 1;

      if (search.trim()) {
        whereClause = `WHERE (
          UPPER(ftbnkcode) LIKE UPPER($${paramIndex}) OR
          UPPER(ftbnkname) LIKE UPPER($${paramIndex + 1})
        )`;
        searchParams = [`%${search}%`, `%${search}%`];
        paramIndex += 2;
      }

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM tmst_bank ${whereClause}`;
      const countResult = await client.query(countQuery, searchParams);
      const totalRecords = parseInt(countResult.rows[0].total);

      // Calculate pagination
      const totalPages = Math.ceil(totalRecords / pageSize);
      const offset = (page - 1) * pageSize;
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      // Validate sort column
      const allowedSortColumns = ['ftbnkid', 'ftbnkcode', 'ftbnkname', 'ftstaactive', 'fddateins', 'fddateupd'];
      const validSortBy = allowedSortColumns.includes(sortBy) ? sortBy : 'ftbnkname';
      const validSortOrder = sortOrder === 'DESC' ? 'DESC' : 'ASC';

      // Get paginated data
      const dataQuery = `
        SELECT ftbnkid, ftbnkcode, ftbnkname, ftstaactive,
               fddateupd, fttimeupd, ftwhoupd,
               fddateins, fttimeins, ftwhoins
        FROM tmst_bank
        ${whereClause}
        ORDER BY ${validSortBy} ${validSortOrder}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const dataParams = [...searchParams, pageSize, offset];
      const result = await client.query(dataQuery, dataParams);

      console.log(`✅ Found ${result.rows.length} banks (Page ${page}/${totalPages}, Total: ${totalRecords})`);

      return {
        success: true,
        message: `Found ${result.rows.length} banks on page ${page}`,
        data: result.rows,
        pagination: {
          currentPage: page,
          totalPages,
          totalRecords,
          pageSize,
          hasNextPage,
          hasPreviousPage
        }
      };

    } catch (error: any) {
      console.error('❌ Error fetching banks:', error.message);
      return {
        success: false,
        message: 'Failed to fetch banks',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Create bank
  ipcMain.handle('create-bank', async (_event, bankData: Omit<BankMaster, 'ftbnkid' | 'fddateins' | 'fttimeins'>): Promise<BankMasterResponse> => {
    let client: Client | null = null;

    try {
      console.log('➕ Creating new bank:', bankData.ftbnkname);
      client = await getDbConnection();

      // Get current timestamp and time
      const now = new Date();
      const currentTime = now.toTimeString().slice(0, 8); // HH:MM:SS format

      const query = `
        INSERT INTO tmst_bank (ftbnkcode, ftbnkname, ftstaactive, fddateins, fttimeins, ftwhoins)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING ftbnkid, ftbnkcode, ftbnkname, ftstaactive,
                  fddateupd, fttimeupd, ftwhoupd,
                  fddateins, fttimeins, ftwhoins
      `;

      const values = [
        bankData.ftbnkcode.toUpperCase(), // Ensure uppercase
        bankData.ftbnkname,
        bankData.ftstaactive || '1',
        now, // fddateins
        currentTime, // fttimeins
        bankData.ftwhoins || 'SYSTEM'
      ];

      const result = await client.query(query, values);
      console.log('✅ Bank created successfully:', result.rows[0].ftbnkname);

      return {
        success: true,
        message: 'Bank created successfully',
        data: result.rows[0]
      };

    } catch (error: any) {
      console.error('❌ Error creating bank:', error.message);

      let errorMessage = 'Failed to create bank';
      if (error.code === '23505') { // Unique constraint violation
        errorMessage = 'Bank code already exists';
      } else if (error.message.includes('value too long')) {
        errorMessage = 'Bank code must be 5 characters or less';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Update bank
  ipcMain.handle('update-bank', async (_event, bankId: number, bankData: Omit<BankMaster, 'ftbnkid' | 'fddateins' | 'fttimeins'>): Promise<BankMasterResponse> => {
    let client: Client | null = null;

    try {
      console.log('✏️ Updating bank ID:', bankId);
      client = await getDbConnection();

      // Get current timestamp and time
      const now = new Date();
      const currentTime = now.toTimeString().slice(0, 8); // HH:MM:SS format

      const query = `
        UPDATE tmst_bank
        SET ftbnkcode = $1, ftbnkname = $2, ftstaactive = $3,
            fddateupd = $4, fttimeupd = $5, ftwhoupd = $6
        WHERE ftbnkid = $7
        RETURNING ftbnkid, ftbnkcode, ftbnkname, ftstaactive,
                  fddateupd, fttimeupd, ftwhoupd,
                  fddateins, fttimeins, ftwhoins
      `;

      const values = [
        bankData.ftbnkcode.toUpperCase(), // Ensure uppercase
        bankData.ftbnkname,
        bankData.ftstaactive || '1',
        now, // fddateupd
        currentTime, // fttimeupd
        bankData.ftwhoupd || 'SYSTEM',
        bankId
      ];

      const result = await client.query(query, values);

      if (result.rows.length === 0) {
        return {
          success: false,
          message: 'Bank not found',
          error: 'BANK_NOT_FOUND'
        };
      }

      console.log('✅ Bank updated successfully:', result.rows[0].ftbnkname);

      return {
        success: true,
        message: 'Bank updated successfully',
        data: result.rows[0]
      };

    } catch (error: any) {
      console.error('❌ Error updating bank:', error.message);

      let errorMessage = 'Failed to update bank';
      if (error.code === '23505') { // Unique constraint violation
        errorMessage = 'Bank code already exists';
      } else if (error.message.includes('value too long')) {
        errorMessage = 'Bank code must be 5 characters or less';
      }

      return {
        success: false,
        message: errorMessage,
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  // Delete bank
  ipcMain.handle('delete-bank', async (_event, bankId: number): Promise<BankMasterResponse> => {
    let client: Client | null = null;

    try {
      console.log('🗑️ Deleting bank ID:', bankId);
      client = await getDbConnection();

      const query = `
        DELETE FROM tmst_bank
        WHERE ftbnkid = $1
        RETURNING ftbnkname
      `;

      const result = await client.query(query, [bankId]);

      if (result.rows.length === 0) {
        return {
          success: false,
          message: 'Bank not found',
          error: 'BANK_NOT_FOUND'
        };
      }

      console.log('✅ Bank deleted successfully:', result.rows[0].ftbnkname);

      return {
        success: true,
        message: 'Bank deleted successfully'
      };

    } catch (error: any) {
      console.error('❌ Error deleting bank:', error.message);
      return {
        success: false,
        message: 'Failed to delete bank',
        error: error.message
      };
    } finally {
      if (client) {
        await client.end();
      }
    }
  });

  console.log('IPC handlers setup complete');
}